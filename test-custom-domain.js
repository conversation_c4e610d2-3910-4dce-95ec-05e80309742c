const http = require('http');

// Test with the custom domain
const customDomain = 'ng-customer-local.dev.dev1.ngnair.com';
const port = 3060;

// Your cookie value
const cookieValue = 'access_token=3x5%2FN0eVt6W5Jxd4dPgQCeVqiYHEo3WBRlA%2B%2FsFmbNmRPgipBeoQo6F8mLkzDhU%2FSUs%2BVO1ty1LDLAlNDRHMAJE9kK1QOioGSAi%2B%2F6K1BltHNjSGM8rib5QFk7wonWGVRSq3UzE88TEUOyVoVN4ebK9zfvDcBNruj4qeziFfr1L0RYrq76scETmdrxRk6%2Fyg7QgjGfW4fHxcyuPAaoRkExjpFN6CWMF498LsOIGGn%2FYTfngVPKwZJYBtXrnh8nesOn0nC8jRnNuKe95tXO3wIjQ80KQBEPX%2BTRjj4qDP%2BLn3Lfwp8Q0cwOazrtOMLLxbPJYti6bW0ZOcljTu4aH0s450nGZPOAE3TrxhfFh3ZlS5KNe2%2B8nwRLM6j9boU9Hi8FSjkhGZlAKfsakfIYnfsr4TSG3xgMLSssHGXSvhsdsukyXRgjhBodMw2C2%2Fn4MHoGptJc6L%2BI7UvS%2FdLuWm61CaC6DNLemzuS2K%2Fd4TcGj1yFUg3vUzuSCOM8c%2F%2BxSE30%2Fgowq%2BtTH%2Bktjm3ZINiIZsEXrgL%2BXmfUDXwddw7BfvsrFH5HOOSra%2F2e%2FUPiKBivqOhSBIxBZ%2FS6zTWsxLqKkMLBJVMoydAJRUoZAHA6ZBJQABs9I9H88h5q%2FwY3nDzBmE7WwCIRcPHt70hhpr8bX1SMpoQBMJ%2BBOfkXVSio%2BjfjQPb7%2FIyOxuvoEFyMgr5gEX6BJRYdrqf84bs7uDnvh%2Bb8rDlyUC3qqEVyHlOkVNs9zpSl1SWKbIpaYuPK352Xz0OoZD%2FVcGC9AqwwP2%2FmC6o6atub%2Fy7WRIIOowkoTzcRmslTac1%2FCxy9casZbOVRwQys2T%2F4RIaImsPK5wuJ6Kyz7RXhr8Nd1KyNrPDvegFitvMcpIzqgwkwySafWVRm3GadU8uI6uwJAyiiu43%2BNE9w8L8ZweeSjJy2oC%2F94oJkFFbXeyE6y8l5NLjraKXzNpky1N7Y88gTH9--Xjk8tJZ9ZLnyzsS4--3dFsz8YkF%2F2xIT%2B0B8CMHA%3D%3D';

function testCustomDomain() {
  console.log(`🔍 Testing custom domain: ${customDomain}:${port}`);
  console.log('🍪 Cookie length:', cookieValue.length);

  // Test GraphQL with custom domain
  const graphqlQuery = {
    query: `query MeCookies {
      meCookies {
        id
        email
        username
        firstName
        lastName
        role
        permissions
        createdAt
        updatedAt
      }
    }`
  };

  const options = {
    hostname: customDomain,
    port: port,
    path: '/graphql',
    method: 'POST',
    headers: {
      'Cookie': cookieValue,
      'Content-Type': 'application/json',
      'Origin': `http://${customDomain}:${port}`,
      'Referer': `http://${customDomain}:${port}/graphql`,
    }
  };

  console.log('📡 Making request to:', `http://${customDomain}:${port}/graphql`);
  console.log('🔧 Headers:', options.headers);

  const req = http.request(options, (res) => {
    console.log('📊 Status Code:', res.statusCode);
    console.log('📋 Response Headers:', res.headers);

    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      console.log('📄 GraphQL Response:');
      try {
        const parsed = JSON.parse(data);
        console.log(JSON.stringify(parsed, null, 2));
        
        if (parsed.errors) {
          console.log('\n❌ GraphQL Errors detected:');
          parsed.errors.forEach((error, index) => {
            console.log(`Error ${index + 1}: ${error.message}`);
          });
        }
        
        if (parsed.data && parsed.data.meCookies) {
          console.log('\n✅ GraphQL authentication successful!');
        }
      } catch (e) {
        console.log('Raw response:', data);
      }
    });
  });

  req.on('error', (e) => {
    console.error('❌ Request error:', e.message);
    console.error('💡 Make sure the custom domain is configured in your hosts file or DNS');
  });

  req.write(JSON.stringify(graphqlQuery));
  req.end();
}

// Test debug endpoint first
function testDebugEndpoint() {
  console.log(`🔍 Testing debug endpoint: ${customDomain}:${port}`);

  const options = {
    hostname: customDomain,
    port: port,
    path: '/auth/debug/cookies',
    method: 'GET',
    headers: {
      'Cookie': cookieValue,
      'Content-Type': 'application/json',
    }
  };

  const req = http.request(options, (res) => {
    console.log('📊 Debug Status Code:', res.statusCode);

    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      console.log('📄 Debug Response:');
      try {
        const parsed = JSON.parse(data);
        console.log(JSON.stringify(parsed, null, 2));
        
        if (parsed.hasAccessToken) {
          console.log('\n✅ Cookie parsed successfully, testing GraphQL...');
          setTimeout(testCustomDomain, 1000);
        } else {
          console.log('\n❌ Cookie not parsed correctly');
        }
      } catch (e) {
        console.log('Raw response:', data);
      }
    });
  });

  req.on('error', (e) => {
    console.error('❌ Debug request error:', e.message);
    console.error('💡 Make sure the custom domain is configured in your hosts file or DNS');
  });

  req.end();
}

// Run tests
testDebugEndpoint();
