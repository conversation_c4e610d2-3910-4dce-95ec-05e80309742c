// Use built-in fetch in Node.js 18+

async function testGraphQL() {
  const url = 'http://ng-customer-local.dev.dev1.ngnair.com:3060/graphql';
  const cookie = 'access_token=3x5%2FN0eVt6W5Jxd4dPgQCeVqiYHEo3WBRlA%2B%2FsFmbNmRPgipBeoQo6F8mLkzDhU%2FSUs%2BVO1ty1LDLAlNDRHMAJE9kK1QOioGSAi%2B%2F6K1BltHNjSGM8rib5QFk7wonWGVRSq3UzE88TEUOyVoVN4ebK9zfvDcBNruj4qeziFfr1L0RYrq76scETmdrxRk6%2Fyg7QgjGfW4fHxcyuPAaoRkExjpFN6CWMF498LsOIGGn%2FYTfngVPKwZJYBtXrnh8nesOn0nC8jRnNuKe95tXO3wIjQ80KQBEPX%2BTRjj4qDP%2BLn3Lfwp8Q0cwOazrtOMLLxbPJYti6bW0ZOcljTu4aH0s450nGZPOAE3TrxhfFh3ZlS5KNe2%2B8nwRLM6j9boU9Hi8FSjkhGZlAKfsakfIYnfsr4TSG3xgMLSssHGXSvhsdsukyXRgjhBodMw2C2%2Fn4MHoGptJc6L%2BI7UvS%2FdLuWm61CaC6DNLemzuS2K%2Fd4TcGj1yFUg3vUzuSCOM8c%2F%2BxSE30%2Fgowq%2BtTH%2Bktjm3ZINiIZsEXrgL%2BXmfUDXwddw7BfvsrFH5HOOSra%2F2e%2FUPiKBivqOhSBIxBZ%2FS6zTWsxLqKkMLBJVMoydAJRUoZAHA6ZBJQABs9I9H88h5q%2FwY3nDzBmE7WwCIRcPHt70hhpr8bX1SMpoQBMJ%2BBOfkXVSio%2BjfjQPb7%2FIyOxuvoEFyMgr5gEX6BJRYdrqf84bs7uDnvh%2Bb8rDlyUC3qqEVyHlOkVNs9zpSl1SWKbIpaYuPK352Xz0OoZD%2FVcGC9AqwwP2%2FmC6o6atub%2Fy7WRIIOowkoTzcRmslTac1%2FCxy9casZbOVRwQys2T%2F4RIaImsPK5wuJ6Kyz7RXhr8Nd1KyNrPDvegFitvMcpIzqgwkwySafWVRm3GadU8uI6uwJAyiiu43%2BNE9w8L8ZweeSjJy2oC%2F94oJkFFbXeyE6y8l5NLjraKXzNpky1N7Y88gTH9--Xjk8tJZ9ZLnyzsS4--3dFsz8YkF%2F2xIT%2B0B8CMHA%3D%3D';

  console.log('Testing debugCookies query...');
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': cookie
      },
      body: JSON.stringify({
        query: 'query DebugCookies { debugCookies }'
      })
    });

    const result = await response.json();
    console.log('Debug Cookies Response:', JSON.stringify(result, null, 2));
    
    if (result.data && result.data.debugCookies) {
      const debugInfo = JSON.parse(result.data.debugCookies);
      console.log('\nParsed Debug Info:', JSON.stringify(debugInfo, null, 2));
    }
  } catch (error) {
    console.error('Error testing debugCookies:', error);
  }

  console.log('\nTesting meCookies query...');
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': cookie
      },
      body: JSON.stringify({
        query: 'query MeCookies { meCookies { id email username firstName lastName role permissions createdAt updatedAt } }'
      })
    });

    const result = await response.json();
    console.log('Me Cookies Response:', JSON.stringify(result, null, 2));
  } catch (error) {
    console.error('Error testing meCookies:', error);
  }
}

testGraphQL();
